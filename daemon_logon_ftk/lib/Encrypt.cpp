#include "Encrypt.h"
#include "ksbase64.h"
#include "logonUtil.h"

// OpenSSL version compatibility handling
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
    // Use EVP API for OpenSSL 3.x
    #include <openssl/evp.h>
    #include <openssl/rand.h>

    // AES CTR encryption function for OpenSSL 3.x
    void openssl3_aes_ctr128_encrypt(const unsigned char *in, unsigned char *out,
                                   size_t length, const unsigned char *key,
                                   unsigned char ivec[AES_BLOCK_SIZE],
                                   unsigned char ecount_buf[AES_BLOCK_SIZE],
                                   unsigned int *num, int key_size) {
        EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
        if (!ctx) return;

        // Select appropriate cipher based on key size
        const EVP_CIPHER *cipher;
        if (key_size == 256) {
            cipher = EVP_aes_256_ctr();
        } else {
            cipher = EVP_aes_128_ctr();  // Default to AES-128
        }

        EVP_EncryptInit_ex(ctx, cipher, NULL, key, ivec);

        int outlen;
        EVP_EncryptUpdate(ctx, out, &outlen, in, length);

        EVP_CIPHER_CTX_free(ctx);
    }

    //#define AES_ctr128_encrypt openssl3_aes_ctr128_encrypt

#elif defined(CENTOS_7X)
    // CentOS 7.x (OpenSSL 1.0.2) - use modes.h, AES_ctr128_encrypt function
#elif defined(CENTOS_6X) || defined(OPENSSL_10X)
    // CentOS 6.x (OpenSSL 1.0.1) - AES_ctr128_encrypt may be in different location
    // Add necessary function declarations directly
    #ifndef AES_ctr128_encrypt
    extern "C" {
        void AES_ctr128_encrypt(const unsigned char *in, unsigned char *out,
                               size_t length, const AES_KEY *key,
                               unsigned char ivec[AES_BLOCK_SIZE],
                               unsigned char ecount_buf[AES_BLOCK_SIZE],
                               unsigned int *num);
    }
    #endif
#endif

void Encrypt::set_key()
{
	memset(iv, 0x00, sizeof(iv));
	strcpy((char*)ckey, "***");
	current_key_size = 128;  // Default to AES-128
}

void Encrypt::set_key(const char *key)
{
	string in_key = key;
	string sha_key;

	//sha_key = sha256(in_key);

	memset(iv, 0x00, sizeof(iv));

	//memcpy(iv, sha_key.c_str(), 16);
	strcpy((char*)ckey, "***");
	current_key_size = 128;  // Default to AES-128
}

void Encrypt::set_key_from_config(const char *config_key)
{
	// Set IV to all zeros (explicit requirement)
	memset(iv, 0x00, sizeof(iv));

	// Derive AES-128 key (ckey) from the provided config string:
	// - If config_key is 16 bytes or longer, use the first 16 bytes
	// - If shorter, copy and pad with zeros to 16 bytes
	if (config_key && strlen(config_key) >= 16) {
		memcpy(ckey, config_key, 16);
	} else if (config_key) {
		size_t key_len = strlen(config_key);
		memcpy(ckey, config_key, key_len);
		memset(ckey + key_len, 0x00, 16 - key_len);
	} else {
		// Null pointer guard: default to zero key if config_key is NULL
		memset(ckey, 0x00, sizeof(ckey));
	}
	current_key_size = 128;  // Default to AES-128
}

void Encrypt::set_key_with_type(const char *config_key, const char *encrypt_type)
{
	// Set IV to all zeros (explicit requirement)
	memset(iv, 0x00, sizeof(iv));

	// Determine key size based on encrypt type
	int key_size = 128;  // Default to AES-128
	int key_bytes = 16;  // Default to 16 bytes

	if (encrypt_type) {
		if (strncmp(encrypt_type, "aes256_base64", 13) == 0) {
			key_size = 256;
			key_bytes = 32;
		}
		else if (strncmp(encrypt_type, "aes128_base64", 13) == 0 ||
		           strncmp(encrypt_type, "aes_base64", 10) == 0) {
			key_size = 128;
			key_bytes = 16;
		}
	}

	// Set the key based on determined size
	if (config_key && strlen(config_key) >= key_bytes) {
		memcpy(ckey, config_key, key_bytes);
	}
	else if (config_key) {
		size_t key_len = strlen(config_key);
		memcpy(ckey, config_key, key_len);
		memset(ckey + key_len, 0x00, key_bytes - key_len);
	}
	else {
		// Null pointer guard: default to zero key if config_key is NULL
		memset(ckey, 0x00, sizeof(ckey));
	}

	current_key_size = key_size;
}

void Encrypt::set_key_with_default_type(const char *encrypt_type)
{
	// Set IV to all zeros (explicit requirement)
	memset(iv, 0x00, sizeof(iv));

	// Determine key size and set default key based on encrypt type
	if (encrypt_type && strncmp(encrypt_type, "aes256_base64", 13) == 0) {
		// AES-256: need 32-byte key
		strcpy((char*)ckey, "***1234567890123456"); // 32 chars
		current_key_size = 256;
	} else {
		// AES-128: default 16-byte key
		strcpy((char*)ckey, "***");
		current_key_size = 128;
	}
}

void Encrypt::init_ctr(struct ctr_state *state, const unsigned char *iv)
{
	state->num = 0;
    memset(state->ecount, 0, AES_BLOCK_SIZE);
    memset(state->ivec+16 , 0, 16);
    memcpy(state->ivec, iv, 16);
}

// encrypt == decrypt (content is the same)
void Encrypt::encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
	// Use key directly instead of AES_KEY structure in OpenSSL 3.x
#else
	AES_set_encrypt_key(ckey, current_key_size, &ase_key);
#endif

	if( bytes_read < BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, bytes_read, ckey,
			state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		return;
	}
	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, BYTES_SIZE, ckey,
			state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 ){
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, mod_len, ckey, state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
#endif
	}
}

void Encrypt::decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
	// Use key directly instead of AES_KEY structure in OpenSSL 3.x
#else
	AES_set_encrypt_key(ckey, current_key_size, &ase_key);
#endif

	if( bytes_read < BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, bytes_read, ckey, state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		return;
	}

	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, BYTES_SIZE, ckey, state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 )
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, mod_len, ckey, state.ivec, state.ecount, &state.num, current_key_size);
#else
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
#endif
	}

}

std::string Encrypt::sha256(const std::string& input) {
    // Array to store SHA-256 result
    unsigned char hash[SHA256_DIGEST_LENGTH];

    // Initialize SHA-256 context and process data
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, input.c_str(), input.size());
    SHA256_Final(hash, &sha256);

    // Convert result to hexadecimal string
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    return ss.str();
}

/**
 * Common function to decrypt AES Base64 encoded receiver number
 * @param encryptValue - encryption type value from packet
 * @param receiverValue - base64 encoded receiver number
 * @param decryptedReceiver - output buffer for decrypted receiver number
 * @param bufferSize - size of output buffer
 * @param configKey - encryption key from configuration
 * @return 0 on success, -1 on failure
 */
int decryptReceiverNumber(const char* encryptValue, const char* receiverValue,
                         char* decryptedReceiver, size_t bufferSize, const char* configKey)
{
	if (!encryptValue || !receiverValue || !decryptedReceiver) {
		return -1;
	}

	// Check if encryption is AES Base64
	if (strncmp(encryptValue, "aes_base64", 10) == 0 ||
	    strncmp(encryptValue, "aes128_base64", 13) == 0 ||
	    strncmp(encryptValue, "aes256_base64", 13) == 0) {
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode(
			(unsigned char *)receiverValue, strlen(receiverValue), &size);

		if (!receiverNum) {
			return -1;
		}

		Encrypt en;
		en.set_key_with_type(configKey, encryptValue);
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));

		// Copy decrypted result to output buffer
		strncpy(decryptedReceiver, (char*)receiverNum, bufferSize - 1);
		decryptedReceiver[bufferSize - 1] = '\0';

		free(receiverNum);
		return 0;
	} else {
		// No encryption, copy as is
		strncpy(decryptedReceiver, receiverValue, bufferSize - 1);
		decryptedReceiver[bufferSize - 1] = '\0';
		return 0;
	}
}

/**
 * Common function to decrypt AES Base64 encoded receiver number with different key setting
 * Uses set_key() instead of set_key_from_config()
 * @param encryptValue - encryption type value from packet (mData.strEncoding)
 * @param receiverValue - base64 encoded receiver number
 * @param decryptedReceiver - output buffer for decrypted receiver number
 * @param bufferSize - size of output buffer
 * @return 0 on success, -1 on failure
 */
int decryptReceiverNumberWithDefaultKey(const char* encryptValue, const char* receiverValue,
                                       char* decryptedReceiver, size_t bufferSize)
{
	if (!encryptValue || !receiverValue || !decryptedReceiver) {
		return -1;
	}

	// Check if encryption is AES Base64 (with space variation)
	if (strncmp(encryptValue, "aes_base64", 10) == 0 || strncmp(encryptValue, " aes_base64", 11) == 0 ||
	    strncmp(encryptValue, "aes128_base64", 13) == 0 || strncmp(encryptValue, " aes128_base64", 14) == 0 ||
	    strncmp(encryptValue, "aes256_base64", 13) == 0 || strncmp(encryptValue, " aes256_base64", 14) == 0) {
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode(
			(unsigned char *)receiverValue, strlen(receiverValue), &size);

		if (!receiverNum) {
			return -1;
		}

		Encrypt en;
		en.set_key_with_default_type(encryptValue);  // Use default key with appropriate type
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));

		// Copy decrypted result to output buffer
		strncpy(decryptedReceiver, (char*)receiverNum, bufferSize - 1);
		decryptedReceiver[bufferSize - 1] = '\0';

		free(receiverNum);
		return 0;
	} else {
		// No encryption, copy as is
		strncpy(decryptedReceiver, receiverValue, bufferSize - 1);
		decryptedReceiver[bufferSize - 1] = '\0';
		return 0;
	}
}

/**
 * Common function to decrypt AES Base64 encoded variable data (generic for all variable types)
 * @param encryptValue - encryption type value from packet
 * @param variableValue - base64 encoded variable data
 * @param decryptedVariable - output buffer for decrypted variable data
 * @param bufferSize - size of output buffer
 * @param configKey - encryption key from configuration
 * @return 0 on success, -1 on failure
 */
int decryptVariableData(const char* encryptValue, const char* variableValue,
                       char* decryptedVariable, size_t bufferSize, const char* configKey)
{
	if (!encryptValue || !variableValue || !decryptedVariable) {
		return -1;
	}

	// Check if encryption is AES Base64
	if (strncmp(encryptValue, "aes_base64", 10) == 0 ||
	    strncmp(encryptValue, "aes128_base64", 13) == 0 ||
	    strncmp(encryptValue, "aes256_base64", 13) == 0) {
		// Check if variableValue is not empty
		if (strlen(variableValue) == 0) {
			// Empty variable data, just copy empty string
			decryptedVariable[0] = '\0';
			return 0;
		}

		int size;
		unsigned char *variableData = (unsigned char*)__base64_decode(
			(unsigned char *)variableValue, strlen(variableValue), &size);

		if (!variableData) {
			return -1;
		}

		Encrypt en;
		en.set_key_with_type(configKey, encryptValue);
		en.decrypt(variableData, variableData, strlen((char*)variableData));

		// Copy decrypted result to output buffer
		strncpy(decryptedVariable, (char*)variableData, bufferSize - 1);
		decryptedVariable[bufferSize - 1] = '\0';

		free(variableData);
		return 0;
	} else {
		// No encryption, copy as is
		strncpy(decryptedVariable, variableValue, bufferSize - 1);
		decryptedVariable[bufferSize - 1] = '\0';
		return 0;
	}
}


