#ifndef _SENDER_DB_INFO_H_
#define _SENDER_DB_INFO_H_

#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "senderInfo.h"

enum {
    GETCTNID,
    GETMMSID,
    SETSENDQUE,
    SETSENDTBL,
    SETSENDCTNTBL,
    SETSENDQUE_FILTER,
	SETRPTTBL,
	SETSENDQUE_FTALK,
	SETSENDQUE_FTK_V3,
	GETMMSSEQ,
	SETSENDRPT
};

class CSenderDbInfo {
    public:
        TypeMsgDataSnd smsData;
        CLogonDbInfo logonDbInfo;
        char szReserve[128];
        CSenderInfo senderInfo;
        int result;
};
/*
** unsined long long mmsid

class CSenderDbInfoAck {
    public:
        int msgid;
        char szResult[4];
        int mmsid;
        int ctnid;
};
*/
class CSenderDbInfoAck {
    public:
        int msgid;
        char szResult[4];
        long long mmsid;
        int ctnid;
        int seq;
};

//class CSenderDbMMSID {
//    public:
//        int type; /* default : header */
//        char szCid[10+1];
//        int mmsid;
//        int ctnid;
//};
class Header {
    public:
        int type;
        int leng;
};


/*
** unsigned long long mmsid
class CSenderDbMMSTBL {
    public:
        Header header;
//        int type; // default : header
        char szDstAddr[16+1];
        char szCallBack[16+1];
        char szMsgTitle[200+1];
        char szPtnSn[16+1];
        char szResvData[200+1];
        char szCid[10+1];
        int nMsgType;
        int nPriority;
        int nCtnId;
        int nCtnType;
        int nRgnRate;
        int nInterval;
        int nTextCnt;
        int nImgCnt;
        int nAugCnt;
        int nMpCnt;
        int nMMSId;
};
*/
class CSenderDbMMSTBL {
    public:
        Header header;
//        int type; //default : header 
        char szSenderKey[40+1];
        char szDstAddr[16+1];
        char szCallBack[16+1];
        char szMsgTitle[200+1];
        char szPtnSn[16+1];
        char szResvData[200+1];
        char szCid[10+1];
        int nMsgType;
        int nPriority;
        int nCtnId;
        int nCtnType;
        int nRgnRate;
        int nInterval;
        long long nMMSId;
		char szChatBubbleType[30+1];
		char szTargeting[1+1];
		char szTmplCd    [20+1];
		char szAppUserId [20+1];
		char szPushAlarm [1+1];
		char szMessageVariable	[4000+1];
		char szButtonVariable 	[1000+1];
		char szCouponVariable	[1000+1];
		char szImageVariable	[1000+1];
		char szVideoVariable	[1000+1];
		char szCommerceVariable	[1000+1];
		char szCarouselVariable	[2000+1];
		char szResellerCode		[20+1];
		char szUnsubPhone		[20+1];
		char szUnsubAuth		[20+1];
		char szAddContent		[100+1];
		char szAdult			[1+1];
		char szHeader			[60+1];
		char szAttachment		[3000+1];
};


class CSenderDbMMSMSG {
    public:
        Header header;
//        int type; /* default : header */
        char szQName[32+1];
        int nPriority;
        int nCtnId;
        char szCallBack[16+1];
        char szDstAddr[16+1];
        char szMsgTitle[100+1];
        int nCntType;
        char szTxtPath[256+1];
        int nRgnRate;
        int nInterval;
//        int nMMSId;
        long long nMMSId;
};

/*
** unsigned long long mmsid
class CSenderDbMMSID {
    public:
        Header header;
//        int type; //default : header 
        char szCid[10+1];
        int mmsid;
        int ctnid;
};
*/
class CSenderDbMMSID {
    public:
        Header header;
//        int type; //default : header 
        char szCid[10+1];
        long long mmsid;
        int ctnid;
};

class CSenderDbMMSCTNTBL {
    public:
        Header header;
//        int type; /* default : header */
        int nCtnId;
        char szCtnName[50+1];
        char szCtnMime[50+1];
        int nCtnSeq;
        char szCtnSvc[5+1];
};

//변작금지 레포트 정보 때문에 추가 20150910
class CSenderDbMMSRPTTBL {
    public:
        Header header;
        long long nMMSId;
        char szDstAddr[16+1];
        char szCallBack[16+1];
		int res_code;
		char res_text[200];
};

//알림톡 추가 20151103
class CSenderDbMMSMSG_TALK {
    public:
        Header header;
        char szQName[32+1];
        int nPriority;
        char szSenderKey[40+1];
        char szDstAddr[16+1];
        char szTmplCd[30+1];
        char szBtName[50+1];
        char szBtUrl[1000+1];
        char szMsgBody[2000+1];
        char szPartnerKey[40+1];
        long long nMMSId;
};
//친구톡 추가 20160720
class CSenderDbMMSMSG_FTALK {
    public:
        Header header;
        char szQName[32+1];
        int nPriority;
        char szSenderKey[40+1];
        char szDstAddr[16+1];
		char szUserKey[30+1];
		//char szMsgGrpCd[30+1];
        char szMsgBody[2000+1];
        char szBtName[50+1];
        //char szBtUrl[1000+1];
        char szBtUrl[500+1];
        //char szButton[4000+1];
        char szButton[3000+1];
        char szImgPath[100+1];
		//char szImgLink[1000+1];
		char szImgLink[500+1];
		char szResMethod[8+1];
		char szTimeout[2+1];
		char szAdFlag[1+1];
		char szWide[1+1];
		char szKkoImgUrl[500+1];
        long long nMMSId;
};

class CSenderDbMMSMSG_FTALKUP {
public:
	Header header;
	char szQName[32+1];
	int nPriority;
	char szSenderKey[40+1];
	char szDstAddr[16+1];
	//char szUserKey[30+1];
	//char szMsgGrpCd[30+1];
	//char szMsgBody[2000+1];
	//char szBtName[50+1];
	//char szBtUrl[1000+1];
	//char szBtUrl[500+1];
	//char szButton[4000+1];
	//char szButton[3000+1];
	//char szImgPath[100+1];
	//char szImgLink[1000+1];
	//char szImgLink[500+1];
	char szResMethod[8+1];
	char szTimeout[2+1];
	//char szAdFlag[1+1];
	//char szWide[1+1];
	//char szKkoImgUrl[500+1];
	long long nMMSId;
	char szEncoding[20+1];
	char szChatBubbleType[30+1];
	char szTargeting[1+1];
	char szTmplCd    [20+1];
	//char szRepFlag	 [1+1];
	//char szRepTitle [64+1];
	//char szRepText	 [500+1];
	char szAppUserId [20+1];
	char szPushAlarm [1+1];
	char szVarType[20+1];
	char szResellerCode[20+1];
	char szUnsubPhone[20+1];
	char szUnsubAuth[20+1];
	char szAddContent[100+1];
	char szAdult[1+1];
	char szHeaders[60+1];
	char szMessageVariable	[4000+1];
	char szButtonVariable 	[1000+1];
	char szCouponVariable	[1000+1];
	char szImageVariable	[1000+1];
	char szVideoVariable	[1000+1];
	char szCommerceVariable	[1000+1];
	char szCarouselVariable	[2000+1];
	char szAttachment		[3000+1];
	char szReserve			[4000+1];
};

//친구톡 추가 20170622
class CSenderDbMMSSEQ {
    public:
        Header header;
        int seq;
};

//SEND ERROR 시 레포트 누락 때문에 추가 20160822
class CSenderDbMMSRPTQUE {
    public:
        Header header;
        char szCid[10+1];
        char szPtnSn[16+1];
        char szDstAddr[16+1];
        int res_code;
        long long nMMSId;
        char res_text[200];
        int nTelcoId;
        int nType;
};

#endif

