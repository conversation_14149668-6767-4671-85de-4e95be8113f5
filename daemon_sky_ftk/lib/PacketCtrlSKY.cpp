/*
 * PacketCtrlSKY.cpp
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <string>
#include <vector>
#include <arpa/inet.h>
#include <iostream>
#include <sys/stat.h>
#include <errno.h>
#include <fstream>

using namespace std;

#include "PacketCtrlSKY.h"
#include "Encrypt.h"
//#include "ksbase64.h"


int CPacketCtrlSKY::getMsg_BIND_REQ(char* pBuff, char* id, char* pw, int nTp)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN CONNECT\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "ID:%s\r\n", id);
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "PASSWORD:%s\r\n", pw);
	strcat(pBuff, strTemp);

	sprintf(strTemp, "REPORT:%s\r\n", nTp ? "Y" : "N");
	strcat(pBuff, strTemp);

	sprintf(strTemp, "VERSION:KSMSA/1.0.0\r\n");
	strcat(pBuff, strTemp);

	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_DELIVER_REQ(string& sSndBuff, SNDSKY& vtSend)
{
	char strTemp[5000];
	int nTxtCnt = 0;
	int nCtnCnt = 0;
	int nSize = 0;
	//char receiverNum[80];
	int size;
	//unsigned char* receiverNum64;
	char receiverNum[15+1];
	
	memset(strTemp, 0x00, sizeof(strTemp));
	//memset(receiverNum, 0x00, sizeof(receiverNum));
	
	sprintf(receiverNum, "%s", vtSend.s_tran_phone);

	// Initialize encryption if not already done
	if (!encryptionInitialized) {
		// Default to plain text if not set
		encryptionMode = "plain_text";
		encryptionInitialized = true;
	}

	// Encrypt receiver if encryption is enabled
	string finalReceiver = encryptReceiver(string(receiverNum), encryptionMode);

	if (strncmp(vtSend.s_var_type, "vari", 4) == 0) {
		// Variable type
		sSndBuff = "BEGIN FTKUPGRADE\r\n";
	}
	else {
		// Transaction or Free type
		sSndBuff = "BEGIN BRANDFREE\r\n";
	}

	//Common fields	
	sprintf(strTemp, "KEY:%s\r\n", vtSend.s_tran_pr);				sSndBuff += strTemp;
	//sprintf(strTemp, "EXTEND:\r\n");									sSndBuff += strTemp;
	//sprintf(strTemp, "SUBJECT:\r\n");									sSndBuff += strTemp;
	sprintf(strTemp, "RECEIVER:%s\r\n", finalReceiver.c_str());	sSndBuff += strTemp;

	sprintf(strTemp, "ENCRYPT:%s\r\n", encryptionMode.c_str());	sSndBuff += strTemp;

	// Add APPUSERID field
	if(strlen(trim(vtSend.s_app_user_id,strlen(vtSend.s_app_user_id))) > 0 ){
		sprintf(strTemp, "APPUSERID:%s\r\n",vtSend.s_app_user_id);		 sSndBuff += strTemp;
	} 
	//sprintf(strTemp, "SENDER:%s\r\n");								sSndBuff += strTemp;
	//sprintf(strTemp, "SENDER:%s\r\n", vtSend.s_tran_callback);		sSndBuff += strTemp	

	sprintf(strTemp, "SENDERKEY:%s\r\n",vtSend.s_tran_sender_key); sSndBuff += strTemp;

	// Add ENCODING field
	if(strlen(trim(vtSend.s_encoding,strlen(vtSend.s_encoding))) > 0 ){
		sprintf(strTemp, "ENCODING:%s\r\n",vtSend.s_encoding);		 sSndBuff += strTemp;
	}

	// Add MESSAGE_TYPE field (using s_chat_bubble_type)
	if(strlen(trim(vtSend.s_chat_bubble_type,strlen(vtSend.s_chat_bubble_type))) > 0 ){
		sprintf(strTemp, "MSGTYPE:%s\r\n",vtSend.s_chat_bubble_type);		 sSndBuff += strTemp;
	}

	// Add TARGETING field
	if(strlen(trim(vtSend.s_targeting,strlen(vtSend.s_targeting))) > 0 ){
		sprintf(strTemp, "TARGETING:%s\r\n",vtSend.s_targeting);		 sSndBuff += strTemp;
	}

	sprintf(strTemp, "TMPLCD:%s\r\n",vtSend.s_tran_tmpl_cd);		 sSndBuff += strTemp;

	// Add PUSHALARM field
	if(strlen(trim(vtSend.s_push_alarm,strlen(vtSend.s_push_alarm))) > 0 ){
		sprintf(strTemp, "PUSHALARM:%s\r\n",vtSend.s_push_alarm);		 sSndBuff += strTemp;
	}

	sprintf(strTemp, "NATIONCODE:82\r\n");		 sSndBuff += strTemp;

	// if (strlen(trim(vtSend.s_reserved, strlen(vtSend.s_reserved))) > 0) {
	// 	sprintf(strTemp, "RESERVED:%s\r\n", vtSend.s_reserved);	sSndBuff += strTemp;
	// }
	if (strlen(trim(vtSend.s_unsub_phone,strlen(vtSend.s_unsub_phone))) > 0 ){
		sprintf(strTemp, "UNSUBSCRIBE_PHONE:%s\r\n",vtSend.s_unsub_phone);		 sSndBuff += strTemp;
	}
	if (strlen(trim(vtSend.s_unsub_auth,strlen(vtSend.s_unsub_auth))) > 0 ){
		sprintf(strTemp, "UNSUBSCRIBE_AUTH:%s\r\n",vtSend.s_unsub_auth);		 sSndBuff += strTemp;
	}

	cout<<"s_tran_tmpl_cd:"<<vtSend.s_tran_tmpl_cd<<"\n"<<endl;

	if (strncmp(vtSend.s_var_type, "vari", 4) == 0) {
		// Add MESSAGE_VARIABLE field with encryption
		if(strlen(trim(vtSend.s_message_variable,strlen(vtSend.s_message_variable))) > 0 ){
			// Encrypt MESSAGE_VARIABLE if encryption is enabled
			string finalMessageVariable = encryptReceiver(string(vtSend.s_message_variable), encryptionMode);
			sprintf(strTemp, "MESSAGE_VARIABLE:%s\r\n",finalMessageVariable.c_str());		 sSndBuff += strTemp;
		}

		// Add BUTTON_VARIABLE field
		if(strlen(trim(vtSend.s_button_variable,strlen(vtSend.s_button_variable))) > 0 ){
			sprintf(strTemp, "BUTTON_VARIABLE:%s\r\n",vtSend.s_button_variable);		 sSndBuff += strTemp;
		}

		// Add COUPONE_VARIABLE field
		if(strlen(trim(vtSend.s_coupon_variable,strlen(vtSend.s_coupon_variable))) > 0 ){
			sprintf(strTemp, "COUPONE_VARIABLE:%s\r\n",vtSend.s_coupon_variable);		 sSndBuff += strTemp;
		}

		// Add IMAGE_VARIABLE field
		if(strlen(trim(vtSend.s_image_variable,strlen(vtSend.s_image_variable))) > 0 ){
			sprintf(strTemp, "IMAGE_VARIABLE:%s\r\n",vtSend.s_image_variable);		 sSndBuff += strTemp;
		}

		// Add VIDEO_VARIABLE field
		if(strlen(trim(vtSend.s_video_variable,strlen(vtSend.s_video_variable))) > 0 ){
			sprintf(strTemp, "VIDEO_VARIABLE:%s\r\n",vtSend.s_video_variable);		 sSndBuff += strTemp;
		}

		// Add COMMERCE_VARIABLE field
		if(strlen(trim(vtSend.s_commerce_variable,strlen(vtSend.s_commerce_variable))) > 0 ){
			sprintf(strTemp, "COMMERCE_VARIABLE:%s\r\n",vtSend.s_commerce_variable);		 sSndBuff += strTemp;
		}

		// Add CAROUSEL_VARIABLE field
		if(strlen(trim(vtSend.s_carousel_variable,strlen(vtSend.s_carousel_variable))) > 0 ){
			sprintf(strTemp, "CAROUSEL_VARIABLE:%s\r\n",vtSend.s_carousel_variable);		 sSndBuff += strTemp;
		}
	}
	else {
		// Transaction or Free type
		if(strlen(trim(vtSend.s_message_variable,strlen(vtSend.s_message_variable))) > 0 ){
			// Encrypt MESSAGE_VARIABLE if encryption is enabled
			string finalMessageVariable = encryptReceiver(string(vtSend.s_message_variable), encryptionMode);
			sprintf(strTemp, "MESSAGE:%s\r\n",finalMessageVariable.c_str());		 sSndBuff += strTemp;
		}

		if(strlen(trim(vtSend.s_attachment,strlen(vtSend.s_attachment))) > 0 ) {
			memset(strTemp, 0x00, sizeof(strTemp));
			snprintf(strTemp, sizeof(strTemp), "ATTACHMENT:%s\r\n",vtSend.s_attachment);
			sSndBuff += strTemp;
		}

		if(strlen(trim(vtSend.s_carousel_variable,strlen(vtSend.s_carousel_variable))) > 0 ){
			memset(strTemp, 0x00, sizeof(strTemp));
			sprintf(strTemp, "CAROUSEL:%s\r\n",vtSend.s_carousel_variable);
			sSndBuff += strTemp;
		}
	}
		

	cout<<"sSndBuff:"<<sSndBuff.c_str()<<endl;
	
	return sSndBuff.length();

}

int CPacketCtrlSKY::getMsg_PING_REQ(char* pBuff)
{
	char strTemp[100];

	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN PING\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:100\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_PING_RES(char* pBuff, string sKey)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN PONG\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:%s\r\n", sKey.c_str());
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getMsg_REPORT_ACK(char* pBuff, string sKey)
{
	char strTemp[100];
	memset(strTemp, 0x00, sizeof(strTemp));

	sprintf(strTemp, "BEGIN ACK\r\n");
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "KEY:%s\r\n", sKey.c_str());
	strcat(pBuff, strTemp);
	
	sprintf(strTemp, "END\r\n");
	strcat(pBuff, strTemp);
	
	return strlen(pBuff);
}

int CPacketCtrlSKY::getData_BndAck(char* pBuff, vector<string>& vtBndAck)
{
	string strCode;
	string strDesc;
	
	matchString(pBuff, "CODE", strCode);
	matchString(pBuff, "DESC", strDesc);
	
	try {
		vtBndAck.push_back(strCode);
		vtBndAck.push_back(strDesc);
	}
	catch (...) {
		return -1;
	}
	return 1;
}


int CPacketCtrlSKY::getData_SndAck(char* pBuff, vector<string>& vtSndAck)
{
	string strKey;
	string strCode;
	string strDesc;
	
	matchString(pBuff, "KEY", strKey);
	matchString(pBuff, "CODE", strCode);
	matchString(pBuff, "DESC", strDesc);
	
	cout<<"getData_SndAck pBuff:"<<pBuff<<"\n"<<endl;
	cout<<"getData_SndAck strKey:"<<strKey<<"\n"<<endl;
	cout<<"getData_SndAck strCode:"<<strCode<<"\n"<<endl;
	cout<<"getData_SndAck strDesc:"<<strDesc<<"\n"<<endl;
	
	try {
		vtSndAck.push_back(strKey);
		vtSndAck.push_back(strCode);
		vtSndAck.push_back(strDesc);
	}
	catch (...) {
		return -1;
	}
	return 1;
	
}

int CPacketCtrlSKY::getData_Report(char* pBuff, vector<string>& vtReport)
{
	string strKey;
	string strCode;
	string strTime;
	string strDesc;
	string strNet;
	
/*
pbuff Report :BEGIN REPORT
KEY:10972
CODE:1000            
TIME:20131029195455
DESC:succ
NET:KTF
END
*/
	try {	
  	if(strstr(pBuff, "BEGIN REPORT")){
				cout<<"BEGIN REPORT :"<<pBuff<<endl;
				matchString(pBuff, "KEY", strKey);
				matchString(pBuff, "CODE", strCode);
				matchString(pBuff, "TIME", strTime);
				matchString(pBuff, "DESC", strDesc);
  				matchString(pBuff, "NET", strNet);
				
				vtReport.push_back(strKey);
				vtReport.push_back(strCode);
				vtReport.push_back(strTime);
				vtReport.push_back(strDesc);
				vtReport.push_back(strNet);
				return 1;
			}
		else if(strstr(pBuff, "BEGIN PING")){
				matchString(pBuff, "KEY", strKey);
				
				vtReport.push_back(strKey);
				return 2;
		}
		else{
				vtReport.push_back("BEGIN UNKNOWN");
				return 3;
		}  	
	}
	catch (...) {
		return -1;
	}
	return 1;
	
}

int CPacketCtrlSKY::getMsgCode(char* pBuff)
{
	HEADER *head = (HEADER*)pBuff;
	int nType = -1;
	try {
	//	nType = atoi(head->msgType);
	//	nType = head->msgType;
			nType = ntohl(head->msgType);
	}
	catch (...) {
		nType = -1;
	}
	return nType;
}



char* CPacketCtrlSKY::matchString(char* szOrg, char* szTag, string &strVal)
{
        #ifdef DEBUG
    //    printf("CPacketCtrlSKY::matchString:szOrg:[%s]\n",szOrg);
    //    printf("CPacketCtrlSKY::matchString:szTag:[%s]\n",szTag);
    //    printf("CPacketCtrlSKY::matchString:strVal:[%s]\n",strVal.c_str());
        #endif
        char* szStt = NULL;
        char* szEnd = NULL;
        strVal = "";
        strVal.reserve(0);
        if(szOrg == NULL)
        {
                return NULL;//??어??값이 ??으????연??NULL
        }

        if( (szStt = strstr(szOrg,szTag)) )  // szTag 값을 szOrg??서 찾아 ??인??값??반환??여 szStt??????        
		{
                szStt = szStt+strlen(szTag)+1;   // TAG값을 ??외??기??헤 문자??shift
                szEnd = strstr(szStt,"\r\n");   // ??인 구분??의 ??인??값??구함
                if((szEnd - szStt) == 0)        // ??료가 ??을 경우 
                {
                        strVal = "";
                        strVal.reserve(0);
                }
                else                            // ??료가 ??을 경우
                {
                        strVal = "";
                        strVal.reserve(0);
                        if(szEnd == NULL)
                        {

                                strVal.insert(0,szStt);
                        }
                        else
                        {
                                strVal.insert(0,szStt, szEnd-szStt);
                        }
                        #ifdef DEBUG
                   //     printf("CPacketCtrl::matchString:strVal:[%s]\n",strVal.c_str());
                        #endif
                }
        }
        if(szStt == NULL)
        {
                return NULL;
        }
        if(szEnd == NULL)
        {
                return NULL;
        }

        #ifdef DEBUG
    //    printf("CPacketCtrl::matchString:END:[%s]\n",szEnd+2);
        #endif

        return szEnd+2;
}


char* CPacketCtrlSKY::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

bool CPacketCtrlSKY::loadEncryptionConfig(const string& encryptMode, const string& keyPath) {
    encryptionMode = encryptMode;
    keyFilePath = keyPath;

    // If encryption is enabled, load the AES key
    if (encryptionMode == "aes_base64") {
        // Initialize the Encrypt instance if not already done
        if (!m_encryptInstance) {
            m_encryptInstance = new Encrypt();
        }

        if (!keyFilePath.empty()) {
            // Read key from file
            ifstream file(keyFilePath.c_str());
            if (!file.is_open()) {
                cout << "Error: Failed to open encryption key file: " << keyFilePath << endl;
                encryptionMode = "plain_text";  // Fall back to plain text
                return false;
            }

            string keyFromFile;
            getline(file, keyFromFile);
            file.close();

            // Set key from config file content
            m_encryptInstance->set_key_from_config(keyFromFile.c_str());
        } else {
            cout << "Error: No encryption key file specified for aes_base64 mode" << endl;
            encryptionMode = "plain_text";  // Fall back to plain text
            return false;
        }
        cout << "Encryption enabled with AES-128-CTR + Base64 using Encrypt library, key file: " << keyFilePath << endl;
    } else {
        cout << "Encryption mode: " << encryptionMode << endl;
    }

    encryptionInitialized = true;
    return true;
}

void CPacketCtrlSKY::setEncryptionSettings(const string& encryptMode, const string& keyPath) {
    loadEncryptionConfig(encryptMode, keyPath);
}

string CPacketCtrlSKY::encryptReceiver(const string& receiver, const string& mode) {
    if (mode == "aes_base64") {
        try {
            // Initialize the Encrypt instance if not already done
            if (!m_encryptInstance) {
                cout << "Error: Encryption instance not initialized. Key file must be loaded first." << endl;
                return receiver; // Return plain text if encryption not properly initialized
            }

            // Prepare input and output buffers
            int inputLen = receiver.length();
            unsigned char* input = (unsigned char*)receiver.c_str();
            unsigned char* output = new unsigned char[inputLen + 1];
            memset(output, 0, inputLen + 1);

            // Encrypt using Encrypt class
            m_encryptInstance->encrypt(input, output, inputLen);

            // Encode to base64
            int encodedSize;
            char* base64Encoded = (char*)__base64_encode(output, inputLen, &encodedSize);

            string result;
            if (base64Encoded != NULL) {
                result = string(base64Encoded);
                free(base64Encoded);
            } else {
                cout << "Warning: Base64 encoding failed, using plain text" << endl;
                result = receiver;
            }

            delete[] output;
            return result;
        } catch (...) {
            cout << "Warning: Encryption failed, using plain text" << endl;
            return receiver;
        }
    }

    // Default: return plain text
    return receiver;
}

CPacketCtrlSKY::~CPacketCtrlSKY() {
    if (m_encryptInstance) {
        delete m_encryptInstance;
    }
}
