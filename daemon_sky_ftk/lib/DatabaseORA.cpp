/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA.h"
#include <stdio.h>
#include <string.h> 
#include <stdlib.h> 
#include <sqlca.h>
#include <iostream>
using namespace std;

namespace KSKYB
{

char *CDatabaseORA::TrimR(char *szOrg, int leng)
{
	int i = 0;

	for(i = leng -1; 1 >= 0; i--) {
		if(isspace(szOrg[i])){
			szOrg[i] = 0x00;
		}
		else {
			break;
		}
	}
	return szOrg;
}


int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::initThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::initThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::freeThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		cout << "CDatabaseORA::connectToOracle() Error[Parameter is NULL]" << endl;
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::connectToOracle() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::closeFromOracle() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::getSendData(sql_context ctx, int ipart, int tpart, int  iline, vector<SNDSKY>& vtSend)
{
	SNDSKY sw;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int s_tran_type;
	int i_part, i_tpart, i_line;
	char s_tran_pr      	 [32   +1];
	char s_tran_id      	 [20   +1];
	char s_tran_phone   	 [15   +1];
	char s_tran_tmpl_cd 	 [30   +1];
	char s_tran_sender_key [40   +1];
	//char s_tran_button     [4000 +1];
	char s_ttl             [14   +1];
	char s_tran_date       [14   +1];
	char s_tran_method     [8    +1];
	char s_tran_timeout    [5    +1];
	char s_tran_callback	[15  +1];
	char s_reserved			[4000 +1];
	// Additional fields for FTALKUP support
	char s_encoding        [20   +1];
	char s_chat_bubble_type[30   +1];
	char s_targeting       [1    +1];
	char s_app_user_id     [20   +1];
	char s_push_alarm      [1    +1];
	char s_message_variable[4000 +1];
	char s_button_variable [1000 +1];
	char s_coupon_variable [1000 +1];
	char s_image_variable  [1000 +1];
	char s_video_variable  [1000 +1];
	char s_commerce_variable[1000 +1];
	char s_carousel_variable[2000 +1];
	char s_var_type		[20   +1];
	char s_unsub_phone	[20   +1];
	char s_unsub_auth	[20   +1];
	char s_add_content	[100  +1];
	char s_adult			[1    +1];
	char s_header			[60   +1];
	char s_attachment		[3000 +1];
	
	EXEC SQL END DECLARE SECTION;
	
	i_part  = ipart;
	i_tpart = tpart;
	i_line  = iline;
#if (DEBUG >= 5)
		sprintf(tmpLog3, "shs getSendData:i_line [%d]]", i_line);
			log3(tmpLog3, 0, 0);
#endif

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;

	EXEC SQL DECLARE cursor_1 CURSOR FOR
		SELECT  TRAN_PR          TRAN_PR,
            NVL(TRAN_ID,' ')          TRAN_ID,
            TRAN_PHONE       TRAN_PHONE,            
            TRAN_TYPE        TRAN_TYPE,
            TRAN_TMPL_CD     TRAN_TMPL_CD,
            TRAN_SENDER_KEY  TRAN_SENDER_KEY,            
            TO_CHAR(TRAN_DATE + 4320/1440 ,'YYYYMMDDHH24MISS') TTL,
            TO_CHAR(TRAN_DATE,'YYYYMMDDHH24MISS') TRAN_DATE,            
            NVL(TRAN_METHOD,' ')             TRAN_METHOD,
            NVL(TRAN_TIMEOUT,' ')            TRAN_TIMEOUT,
			TRAN_CALLBACK		TRAN_CALLBACK,			
			RESERVED			RESERVED,
			NVL(TRAN_ENCODING,' ')           TRAN_ENCODING,
			NVL(TRAN_CHAT_BUBBLE_TYPE,' ')   TRAN_CHAT_BUBBLE_TYPE,
			NVL(TRAN_TARGETING,' ')          TRAN_TARGETING,
			NVL(TRAN_APP_USER_ID,' ')        TRAN_APP_USER_ID,
			NVL(TRAN_PUSH_ALARM,' ')         TRAN_PUSH_ALARM,
			NVL(TRAN_MESSAGE_VARIABLE,' ')   TRAN_MESSAGE_VARIABLE,
			NVL(TRAN_BUTTON_VARIABLE,' ')    TRAN_BUTTON_VARIABLE,
			NVL(TRAN_COUPON_VARIABLE,' ')    TRAN_COUPON_VARIABLE,
			NVL(TRAN_IMAGE_VARIABLE,' ')     TRAN_IMAGE_VARIABLE,
			NVL(TRAN_VIDEO_VARIABLE,' ')     TRAN_VIDEO_VARIABLE,
			NVL(TRAN_COMMERCE_VARIABLE,' ')  TRAN_COMMERCE_VARIABLE,
			NVL(TRAN_CAROUSEL_VARIABLE,' ')  TRAN_CAROUSEL_VARIABLE,
			NVL(TRAN_VAR_TYPE,' ')           TRAN_VAR_TYPE,
			NVL(TRAN_UNSUB_PHONE,' ')        TRAN_UNSUB_PHONE,
			NVL(TRAN_UNSUB_AUTH,' ')         TRAN_UNSUB_AUTH,
			NVL(TRAN_ADD_CONTENT,' ')        TRAN_ADD_CONTENT,
			NVL(TRAN_ADULT,' ')              TRAN_ADULT,
			NVL(TRAN_HEADER,' ')            TRAN_HEADER,
			NVL(TRAN_ATTACHMENT,' ')         TRAN_ATTACHMENT
       FROM TBL_FTALK_MSG
      WHERE TRAN_STATUS = '1'
        AND TRAN_TYPE = :i_line
        AND TRAN_DATE < SYSDATE
        AND TO_CHAR(TRAN_DATE, 'YYYYMMDDHH24MISS') > TO_CHAR(SYSDATE-4320/1440, 'YYYYMMDDHH24MISS')
        AND ROWNUM < 101
	;

	EXEC SQL OPEN cursor_1;
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getSendData:CURSOR1 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	for(int i = 0;;i++)
	{
		memset(s_tran_pr      	,0x00, sizeof(s_tran_pr      	 ));
		memset(s_tran_id      	,0x00, sizeof(s_tran_id      	 ));
		memset(s_tran_phone   	,0x00, sizeof(s_tran_phone   	 ));
		memset(s_tran_tmpl_cd 	,0x00, sizeof(s_tran_tmpl_cd 	 ));
		memset(s_tran_sender_key,0x00, sizeof(s_tran_sender_key));
		//memset(s_tran_button    ,0x00, sizeof(s_tran_button    ));
		memset(s_ttl            ,0x00, sizeof(s_ttl            ));
		memset(s_tran_date      ,0x00, sizeof(s_tran_date      ));
		memset(s_tran_method    ,0x00, sizeof(s_tran_method     ));
		memset(s_tran_timeout   ,0x00, sizeof(s_tran_timeout   ));
		memset(s_tran_callback  ,0x00, sizeof(s_tran_callback   ));
		memset(s_reserved		,0x00, sizeof(s_reserved   ));
		memset(s_encoding       ,0x00, sizeof(s_encoding   ));
		memset(s_chat_bubble_type,0x00, sizeof(s_chat_bubble_type));
		memset(s_targeting      ,0x00, sizeof(s_targeting   ));
		memset(s_app_user_id    ,0x00, sizeof(s_app_user_id   ));
		memset(s_push_alarm     ,0x00, sizeof(s_push_alarm   ));
		memset(s_message_variable,0x00, sizeof(s_message_variable));
		memset(s_button_variable ,0x00, sizeof(s_button_variable ));
		memset(s_coupon_variable ,0x00, sizeof(s_coupon_variable ));
		memset(s_image_variable  ,0x00, sizeof(s_image_variable  ));
		memset(s_video_variable  ,0x00, sizeof(s_video_variable  ));
		memset(s_commerce_variable,0x00, sizeof(s_commerce_variable));
		memset(s_carousel_variable,0x00, sizeof(s_carousel_variable));
		memset(s_var_type        ,0x00, sizeof(s_var_type        ));
		memset(s_unsub_phone     ,0x00, sizeof(s_unsub_phone     ));
		memset(s_unsub_auth      ,0x00, sizeof(s_unsub_auth      ));
		memset(s_add_content     ,0x00, sizeof(s_add_content     ));
		memset(s_adult           ,0x00, sizeof(s_adult           ));
		memset(s_header         ,0x00, sizeof(s_header         ));
		memset(s_attachment      ,0x00, sizeof(s_attachment      ));


		EXEC SQL FETCH cursor_1
			INTO :s_tran_pr        ,
					 :s_tran_id      	 ,
					 :s_tran_phone   	 ,
					 :s_tran_type      ,
					 :s_tran_tmpl_cd 	 ,
					 :s_tran_sender_key,
					 :s_ttl            ,
					 :s_tran_date      ,
					 :s_tran_method		 ,
					 :s_tran_timeout ,
					 :s_tran_callback	,
					 :s_reserved,
					 :s_encoding,
					 :s_chat_bubble_type,
					 :s_targeting,
					 :s_app_user_id,
					 :s_push_alarm,
					 :s_message_variable,
					 :s_button_variable,
					 :s_coupon_variable,
					 :s_image_variable,
					 :s_video_variable,
					 :s_commerce_variable,
					 :s_carousel_variable,
					 :s_var_type,
					 :s_unsub_phone,
					 :s_unsub_auth,
					 :s_add_content,
					 :s_adult,
					 :s_header,
					 :s_attachment
		;
		
		if(sqlca.sqlcode == 1403)
		{
			EXEC SQL CLOSE cursor_1;
			return 1;
		}
		
#if (DEBUG >= 5)
		sprintf(tmpLog3, "shs getSendData:FETCH1 [%s][%d][%.150s]", s_tran_pr,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);

#endif
		
		TrimR(s_tran_pr      	 ,sizeof(s_tran_pr      	)-1);
		TrimR(s_tran_id      	 ,sizeof(s_tran_id      	)-1);
		TrimR(s_tran_phone   	 ,sizeof(s_tran_phone   	)-1);
		TrimR(s_tran_tmpl_cd 	 ,sizeof(s_tran_tmpl_cd 	)-1);
		TrimR(s_tran_sender_key,sizeof(s_tran_sender_key)-1);
		//TrimR(s_tran_button    ,sizeof(s_tran_button    )-1);
		TrimR(s_ttl            ,sizeof(s_ttl            )-1);
		TrimR(s_tran_date      ,sizeof(s_tran_date      )-1);
		TrimR(s_tran_method    ,sizeof(s_tran_method    )-1);
		TrimR(s_tran_timeout   ,sizeof(s_tran_timeout   )-1);
		TrimR(s_tran_callback  ,sizeof(s_tran_callback  )-1);
		TrimR(s_reserved   ,sizeof(s_reserved)-1);
		// Trim additional FTALKUP fields
		TrimR(s_encoding        ,sizeof(s_encoding        )-1);
		TrimR(s_chat_bubble_type,sizeof(s_chat_bubble_type)-1);
		TrimR(s_targeting       ,sizeof(s_targeting       )-1);
		TrimR(s_app_user_id     ,sizeof(s_app_user_id     )-1);
		TrimR(s_push_alarm      ,sizeof(s_push_alarm      )-1);
		TrimR(s_message_variable,sizeof(s_message_variable)-1);
		TrimR(s_button_variable ,sizeof(s_button_variable )-1);
		TrimR(s_coupon_variable ,sizeof(s_coupon_variable )-1);
		TrimR(s_image_variable  ,sizeof(s_image_variable  )-1);
		TrimR(s_video_variable  ,sizeof(s_video_variable  )-1);
		TrimR(s_commerce_variable,sizeof(s_commerce_variable)-1);
		TrimR(s_carousel_variable,sizeof(s_carousel_variable)-1);
		TrimR(s_var_type        ,sizeof(s_var_type        )-1);
		TrimR(s_unsub_phone     ,sizeof(s_unsub_phone     )-1);
		TrimR(s_unsub_auth      ,sizeof(s_unsub_auth      )-1);
		TrimR(s_add_content     ,sizeof(s_add_content     )-1);
		TrimR(s_adult           ,sizeof(s_adult           )-1);
		TrimR(s_header         ,sizeof(s_header         )-1);
		TrimR(s_attachment      ,sizeof(s_attachment      )-1);

#if (DEBUG >= 5)

		sprintf(tmpLog3, "getSendData s_message_variable [%.500s]", s_message_variable);
		log3(tmpLog3, 0, 0);
#endif

		EXEC SQL WHENEVER SQLERROR CONTINUE;
		EXEC SQL CONTEXT USE :ctx;
		EXEC SQL
		//UPDATE MTS_ATALK_MSGP
		UPDATE TBL_FTALK_MSG
		   SET TRAN_STATUS  = '6'
		 WHERE TRAN_PR = TRIM(:s_tran_pr);

		memset(&sw, 0x00, sizeof(sw));
		snprintf(sw.s_tran_pr 				, sizeof(s_tran_pr   	 ), s_tran_pr  							 );
		snprintf(sw.s_tran_id 				, sizeof(s_tran_id   	 ), s_tran_id  							 );
		snprintf(sw.s_tran_phone   		, sizeof(s_tran_phone	 ), s_tran_phone       			 );
		memcpy  (sw.s_tran_tmpl_cd 		, s_tran_tmpl_cd  			, sizeof(s_tran_tmpl_cd 	));
		memcpy  (sw.s_tran_sender_key , s_tran_sender_key   	, sizeof(s_tran_sender_key));
		//memcpy  (sw.s_tran_button   	, s_tran_button    			, sizeof(s_tran_button		));
		snprintf(sw.s_ttl     				, sizeof(s_ttl     	 	 ), s_ttl      							 );
		snprintf(sw.s_tran_date     	, sizeof(s_tran_date 	 ), s_tran_date      				 );
		memcpy  (sw.s_tran_method   	, s_tran_method    			, sizeof(s_tran_method		));
		memcpy  (sw.s_tran_timeout 		, s_tran_timeout   			, sizeof(s_tran_timeout		));
		memcpy  (sw.s_tran_callback 	, s_tran_callback  			, sizeof(s_tran_callback	));
		memcpy  (sw.s_reserved	, s_reserved   		, sizeof(s_reserved		));
		// Copy additional FTALKUP fields
		memcpy  (sw.s_encoding        , s_encoding        , sizeof(s_encoding        ));
		memcpy  (sw.s_chat_bubble_type, s_chat_bubble_type, sizeof(s_chat_bubble_type));
		memcpy  (sw.s_targeting       , s_targeting       , sizeof(s_targeting       ));
		memcpy  (sw.s_app_user_id     , s_app_user_id     , sizeof(s_app_user_id     ));
		memcpy  (sw.s_push_alarm      , s_push_alarm      , sizeof(s_push_alarm      ));
		memcpy  (sw.s_message_variable, s_message_variable, sizeof(s_message_variable));
		memcpy  (sw.s_button_variable , s_button_variable , sizeof(s_button_variable ));
		memcpy  (sw.s_coupon_variable , s_coupon_variable , sizeof(s_coupon_variable ));
		memcpy  (sw.s_image_variable  , s_image_variable  , sizeof(s_image_variable  ));
		memcpy  (sw.s_video_variable  , s_video_variable  , sizeof(s_video_variable  ));
		memcpy  (sw.s_commerce_variable, s_commerce_variable, sizeof(s_commerce_variable));
		memcpy  (sw.s_carousel_variable, s_carousel_variable, sizeof(s_carousel_variable));
		memcpy  (sw.s_var_type        , s_var_type        , sizeof(s_var_type        ));
		memcpy  (sw.s_unsub_phone     , s_unsub_phone     , sizeof(s_unsub_phone     ));
		memcpy  (sw.s_unsub_auth      , s_unsub_auth      , sizeof(s_unsub_auth      ));
		memcpy  (sw.s_add_content     , s_add_content     , sizeof(s_add_content     ));
		memcpy  (sw.s_adult           , s_adult           , sizeof(s_adult           ));
		memcpy  (sw.s_header         , s_header         , sizeof(s_header         ));
		memcpy  (sw.s_attachment      , s_attachment      , sizeof(s_attachment      ));

#if (DEBUG >= 5)
		sprintf(tmpLog3, "getSendData: sw.s_message_variable [%.500s]", sw.s_message_variable);
		log3(tmpLog3, 0, 0);
		sprintf(tmpLog3, "getSendData: sw.s_reserved [%.1000s]", sw.s_reserved);
		log3(tmpLog3, 0, 0);
#endif
		vtSend.push_back(sw);
		
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "getSendData:FETCH1 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			EXEC SQL CLOSE cursor_1;
			return -1;
		}
		
	}
	
	EXEC SQL CLOSE cursor_1;

	return 1;
}


int CDatabaseORA::setWaitUpdate(sql_context ctx, char* status, SNDSKY& vtSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	char s_Tran_status [1+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(s_tran_pr			, 0x00, sizeof(s_tran_pr));
	memset(s_Tran_status 	, 0x00, sizeof(s_Tran_status ));
	
	sprintf(s_tran_pr,"%s", vtSend.s_tran_pr);
	strcpy(s_Tran_status 	, status          );

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE MTS_ATALK_MSGP
		UPDATE TBL_FTALK_MSG
		   SET TRAN_STATUS  = :s_Tran_status
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	//sprintf(tmpLog3, "shs setWaitUpdate:MTS_ATALK_MSGP UPDATE::[%s][%s][%d]", s_tran_pr,s_Tran_status,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
		
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "setWaitUpdate:TBL_ATALK_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setSndAckData(sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	EXEC SQL END DECLARE SECTION;

	memset(s_tran_pr, 0x00, sizeof(s_tran_pr));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();
	/*
	 * vtSndAck list
	 * 0:KEY
	 * 1:CODE
	 * 2:DESC
	 */
	strncpy(s_tran_pr, string(*(itrData)).c_str(), sizeof(s_tran_pr)-1);
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		//UPDATE MTS_ATALK_MSGP
		UPDATE TBL_FTALK_MSG
		   SET TRAN_STATUS   = '2'
		     , TRAN_SENDDATE = SYSDATE
		 WHERE TRAN_PR  = TRIM(:s_tran_pr)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//sprintf(tmpLog3, "shs setSndAckData:MTS_ATALK_MSGP UPDATE::[%s][%d]", s_tran_pr,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
	
	if (sqlca.sqlcode != 0 )
	{
			sprintf(tmpLog3, "setSndAckData:TBL_ATALK_MSG UPDATE Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}

/*
int CDatabaseORA::setSndAckData(sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int nSqlCod, nRetCod, mMsgSeq;
	char sRptCod[2+1], sErrMsg[256];
	EXEC SQL END DECLARE SECTION;

	nSqlCod = nRetCod = mMsgSeq = 0;
	memset(sRptCod, 0x00, sizeof(sRptCod));
	memset(sErrMsg, 0x00, sizeof(sErrMsg));
	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();
	mMsgSeq = atoi(string(*itrData).c_str());
	strcpy(sRptCod, string(*(itrData+1)).c_str());

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		bcdba.proc_set_ackdata(:mMsgSeq, :sRptCod, :nRetCod, :nSqlCod, :sErrMsg);
	END;
	END-EXEC;

	if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114) 
		return -1;
	if (nRetCod!=0) {
		cout << "setSndAckDataErr::["<<mMsgSeq<<"]["<<sRptCod<<"]["<<nRetCod<<"]["<<nSqlCod<<"]["<<sErrMsg<<"]" << endl;
		return -1;
	}
	else
		return 1;
}
*/

int CDatabaseORA::setReportData(sql_context ctx, vector<string>& vtReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
		struct sqlca sqlca;
		int nSqlCod, nRetCod;
		char sMmsId[32+1];
		char sDlvDate[14+1];
		char sResCode[5+1];
	EXEC SQL END DECLARE SECTION;


	nSqlCod = nRetCod = 0;
	memset(sDlvDate, 0x00, sizeof(sDlvDate));
	memset(sResCode, 0x00, sizeof(sResCode));
	
	vector<string>::iterator itrData;
	itrData = vtReport.begin();
	strcpy(sMmsId, string(*itrData).c_str());
	strcpy(sDlvDate, string(*(itrData+2)).c_str());
	strncpy(sResCode, string(*(itrData+1)).c_str(), 5);
	//strcpy(sResCode, string(*(itrData+1)).c_str());
	
	//sprintf(tmpLog3, "shs setReportData ::[%s][%s][%s]", sMmsId, sDlvDate,sResCode);
	//	log3(tmpLog3, 0, 0);
	

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		//UPDATE MTS_ATALK_MSGP
		UPDATE TBL_FTALK_MSG
		   SET TRAN_STATUS   = '3'
		     , TRAN_REPORTDATE = TO_DATE(:sDlvDate,'YYYYMMDDHH24MISS')
		     , TRAN_RSLTDATE = SYSDATE
		     , TRAN_RSLT = :sResCode
		 WHERE TRAN_PR  = TRIM(:sMmsId)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114) 
	//	return -1;

	if (sqlca.sqlcode!=0) {
		//cout << "setReportDataErr::]["<<nRetCod<<"]["<<nSqlCod<<"]["<<sErrMsg<<"]" << endl;
		sprintf(tmpLog3, "setReportData Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

/*
int CDatabaseORA::setTranDate(sql_context ctx, int mms_id)
{
	EXEC SQL BEGIN DECLARE SECTION;
		struct sqlca sqlca;

		int nMmsId;
	EXEC SQL END DECLARE SECTION;


	nMmsId = mms_id;
#ifdef DEBUG
cout << "+++++++++++++++++++++setTranDate mmsid:[" << nMmsId << "]" << endl;
#endif

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		UPDATE TBMMS_SEND 
		SET TRAN_DATE = to_char(sysdate, 'yyyymmddhh24miss')
		WHERE MMS_ID = :nMmsId
		;
	COMMIT;
	END;
	END-EXEC;

#ifdef DEBUG
cout << "+++++++++++++++++++++setTranDate sqlcode:[" << sqlca.sqlcode << "]" << endl;
#endif
	if (sqlca.sqlcode != 0) {
		cout << "setTranDate sqlcode:[" << sqlca.sqlcode << "]" << endl;
		return -1;
	}

	return 0;
}
*/

void CDatabaseORA::log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}

}
