/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <iostream>
using namespace std;

char tmpLog3[1024];
void log3(char *buf, int st, int err);
void get_timestring(char *fmt, long n, char *s);
char* trim(char* szOrg, int leng);
char* removeAllSpaces(char *str);

namespace KSKYB
{
int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::setEnableThreads() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::initThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::freeThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		log3(tmpLog3, 0, 0);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

//int CDatabaseORA::getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend)
long long CDatabaseORA::getMsgData(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[12+1];
    char user_key[30+1];
    char msg_grp_cd[30+1];
    char msg_body[2000+1];
    char button_name[50+1];
    char button_url[1000+1];
    char img_path[100+1];
    char img_link[1000+1];
	
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	char s_mms_id[16+1];

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(telco_name, 0x00, sizeof(telco_name));
	memset(cmms_id,	0x00, sizeof(cmms_id));
	memset(sender_key,	0x00, sizeof(sender_key));
	memset(dst_addr, 0x00, sizeof(dst_addr));
	memset(user_key, 0x00, sizeof(user_key));
	memset(msg_grp_cd, 0x00, sizeof(msg_grp_cd));
	memset(msg_body, 0x00, sizeof(msg_body));
	memset(button_name, 0x00, sizeof(button_name));
	memset(button_url, 0x00, sizeof(button_url));
	memset(img_path, 0x00, sizeof(img_path));
	memset(img_link, 0x00, sizeof(img_link));
	
	memset(s_mms_id, 0x00, sizeof s_mms_id);

	snprintf(telco_name, sizeof(telco_name), q_name);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_friendtalk(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_grp_cd, :msg_body, :button_name, :button_url, :img_path, :img_link, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	
//	sprintf(s_mms_id, "%d", mms_id);
	mms_id = atoll(cmms_id);
	sprintf(s_mms_id, "%s", cmms_id);
	
	switch(ot_sqlcode) {
		case 0:
			mapSend["mms_id"] = trimR(s_mms_id).c_str();
			mapSend["sender_key"] = trimR(sender_key).c_str();
			mapSend["dst_addr"] = trimR(dst_addr).c_str();
			mapSend["user_key"] = trimR(user_key).c_str();
			mapSend["msg_grp_cd"] = trimR(msg_grp_cd).c_str();
			mapSend["msg_body"] = trimR(msg_body).c_str();
			mapSend["button_name"] = trimR(button_name).c_str();
			mapSend["button_url"] = trimR(button_url).c_str();
			mapSend["img_path"] = trimR(img_path).c_str();
			mapSend["img_link"] = trimR(img_link).c_str();
			
			return mms_id;
		case -5:
		case -1405:
			break;
		default:

			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			//sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
			sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s][%s]", ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}

	return 0;
}

long long CDatabaseORA::getMsgData_V2(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char telco_name[24+1];
    long long mms_id;
    char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[16+1];
    char user_key[30+1];
    char msg_body[2000+1];
    char button_name[50+1];
    char button_url[1000+1];
    char img_path[100+1];
    char img_link[1000+1];
    char res_method[8+1];
    char timeout[2+1];

    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;
    EXEC SQL END DECLARE SECTION;

    EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

    char s_mms_id[16+1];

    memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
    memset(telco_name, 0x00, sizeof(telco_name));
    memset(cmms_id, 0x00, sizeof(cmms_id));
    memset(sender_key,  0x00, sizeof(sender_key));
    memset(dst_addr, 0x00, sizeof(dst_addr));
    memset(user_key, 0x00, sizeof(user_key));
    memset(msg_body, 0x00, sizeof(msg_body));
    memset(button_name, 0x00, sizeof(button_name));
    memset(button_url, 0x00, sizeof(button_url));
    memset(img_path, 0x00, sizeof(img_path));
    memset(img_link, 0x00, sizeof(img_link));
    memset(res_method, 0x00, sizeof(res_method));
    memset(timeout, 0x00, sizeof(timeout));

    memset(s_mms_id, 0x00, sizeof s_mms_id);

    snprintf(telco_name, sizeof(telco_name), q_name);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL EXECUTE
        BEGIN
        proc_get_ftalk_msg(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :img_path, :img_link, :res_method, :timeout, :ot_sqlcode, :ot_sqlmsg);
    END;
    END-EXEC;


    mms_id = atoll(cmms_id);
    sprintf(s_mms_id, "%s", cmms_id);

    switch(ot_sqlcode) {
        case 0:
            mapSend["mms_id"] = trimR(s_mms_id).c_str();
            mapSend["sender_key"] = trimR(sender_key).c_str();
            mapSend["dst_addr"] = trimR(dst_addr).c_str();
            mapSend["user_key"] = trimR(user_key).c_str();
            mapSend["msg_body"] = trimR(msg_body).c_str();
            mapSend["button_name"] = trimR(button_name).c_str();
            mapSend["button_url"] = trimR(button_url).c_str();
            mapSend["img_path"] = trimR(img_path).c_str();
            mapSend["img_link"] = trimR(img_link).c_str();
            mapSend["res_method"] = trimR(res_method).c_str();
            mapSend["timeout"] = trimR(timeout).c_str();

            return mms_id;
        case -5:
        case -1405:
            break;
        default:

            //sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
            //sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
            sprintf(tmpLog3, "CDatabaseORA::getMsgData_V2() ERROR[%d][%s][%s]", ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
            log3(tmpLog3, 0, 0);
            return -1;
    }

    return 0;
}

long long CDatabaseORA::getMsgData_V3(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char telco_name[24+1];
    long long mms_id;
    char cmms_id[30+1];
    char sender_key[40+1];
    char dst_addr[16+1];
    char user_key[30+1];
    char msg_body[2000+1];
    char button_name[50+1];
    //char button_url[1000+1];
    char button_url[500+1];
    //char button[4000+1];
    char button[3000+1];
    char img_path[100+1];
    //char img_link[1000+1];
    char img_link[500+1];
    char res_method[8+1];
    char timeout[2+1];
    char ad_flag[1+1];
    char wide[1+1];
	char kko_img_url[500+1];

    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;
    EXEC SQL END DECLARE SECTION;

    EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

    char s_mms_id[16+1];

    memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
    memset(telco_name, 0x00, sizeof(telco_name));
    memset(cmms_id, 0x00, sizeof(cmms_id));
    memset(sender_key,  0x00, sizeof(sender_key));
    memset(dst_addr, 0x00, sizeof(dst_addr));
    memset(user_key, 0x00, sizeof(user_key));
    memset(msg_body, 0x00, sizeof(msg_body));
    memset(button_name, 0x00, sizeof(button_name));
    memset(button_url, 0x00, sizeof(button_url));
    memset(button, 0x00, sizeof(button));
    memset(img_path, 0x00, sizeof(img_path));
    memset(img_link, 0x00, sizeof(img_link));
    memset(res_method, 0x00, sizeof(res_method));
    memset(timeout, 0x00, sizeof(timeout));
    memset(ad_flag, 0x00, sizeof(ad_flag));
    memset(wide, 0x00, sizeof(wide));
	memset(kko_img_url, 0x00, sizeof(wide));
    memset(s_mms_id,    0x00, sizeof(s_mms_id));

    snprintf(telco_name, sizeof(telco_name), q_name);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL EXECUTE
        BEGIN
        //proc_get_ftalk_msg(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :img_path, :img_link, :res_method, :timeout, :ot_sqlcode, :ot_sqlmsg);
        //proc_get_ftk_msg_v3(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :button, :img_path, :img_link, :res_method, :timeout, :ad_flag, :wide,:ot_sqlcode, :ot_sqlmsg);
		
		proc_get_ftk_msg_v3(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :button, :img_path, :img_link, :res_method, :timeout, :ad_flag, :wide, :kko_img_url, :ot_sqlcode, :ot_sqlmsg);
    END;
    END-EXEC;


    //mms_id = atoll(cmms_id);
    //sprintf(s_mms_id, "%s", cmms_id);

    switch(ot_sqlcode) {
        case 0:
        	mms_id = atoll(cmms_id);
    		snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);
    
            mapSend["mms_id"] = trimR(s_mms_id).c_str();
            mapSend["sender_key"] = trimR(sender_key).c_str();
            mapSend["dst_addr"] = trimR(dst_addr).c_str();
            mapSend["user_key"] = trimR(user_key).c_str();
            mapSend["msg_body"] = trimR(msg_body).c_str();
            mapSend["button_name"] = trimR(button_name).c_str();
            mapSend["button_url"] = trimR(button_url).c_str();
            mapSend["button"] = trimR(button).c_str();
            mapSend["img_path"] = trimR(img_path).c_str();
            mapSend["img_link"] = trimR(img_link).c_str();
            mapSend["res_method"] = trimR(res_method).c_str();
            mapSend["timeout"] = trimR(timeout).c_str();
            mapSend["ad_flag"] = trimR(ad_flag).c_str();
            mapSend["wide"] = trimR(wide).c_str();
			mapSend["kko_img_url"] = trimR(kko_img_url).c_str();

            return mms_id;
        case -5:
        case -1405:
            break;
        default:

            //sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
            //sprintf(tmpLog3, "CDatabaseORA::getMsgData() ERROR[%d][%s]", ot_sqlcode, ot_sqlmsg );
            //sprintf(tmpLog3, "CDatabaseORA::getMsgData_V2() ERROR[%d][%s][%s]", ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
            //sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR MMSID[%lld][%d][%s][%s]", mms_id, ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
            sprintf(tmpLog3, "CDatabaseORA::getMsgData_V3() ERROR [%d][%s][%s]", ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
            log3(tmpLog3, 0, 0);
            return -1;
    }

    return 0;
}

long long CDatabaseORA::getMsgData_V4(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char telco_name[24+1];
    long long mms_id;
    char cmms_id[30+1];
	char var_type[20+1];
    char sender_key[40+1];
    char dst_addr[16+1];
    char res_method[8+1];    
    char encoding_type[20+1];
    char chat_bubble_type[30+1];
    char targeting[1+1];
    char tmpl_cd[20+1];
    char app_user_id[20+1];
    char push_alarm[1+1];
	char reseller_code[20+1];
	char unsub_phone[20+1];
	char unsub_auth[20+1];
	char add_content[100+1];
	char adult[1+1];
	char headers[60+1];
    char message_variable[4000+1];
    char button_variable[1000+1];
    char coupon_variable[1000+1];
    char image_variable[1000+1];
    char video_variable[1000+1];
    char commerce_variable[1000+1];
    char carousel_variable[2000+1];
	char attachment[3000+1];
    char reserve[4000+1];    

    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;
    EXEC SQL END DECLARE SECTION;

    EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

    char s_mms_id[16+1];

    memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
    memset(telco_name, 0x00, sizeof(telco_name));
    memset(cmms_id, 0x00, sizeof(cmms_id));
    memset(sender_key,  0x00, sizeof(sender_key));
    memset(dst_addr, 0x00, sizeof(dst_addr));
    
    memset(res_method, 0x00, sizeof(res_method));  
    
    memset(s_mms_id,    0x00, sizeof(s_mms_id));
    memset(encoding_type, 0x00, sizeof(encoding_type));
    memset(chat_bubble_type, 0x00, sizeof(chat_bubble_type));
    memset(targeting, 0x00, sizeof(targeting));
    memset(tmpl_cd, 0x00, sizeof(tmpl_cd));
    memset(app_user_id, 0x00, sizeof(app_user_id));
    memset(push_alarm, 0x00, sizeof(push_alarm));
    memset(message_variable, 0x00, sizeof(message_variable));
    memset(button_variable, 0x00, sizeof(button_variable));
    memset(coupon_variable, 0x00, sizeof(coupon_variable));
    memset(image_variable, 0x00, sizeof(image_variable));
    memset(video_variable, 0x00, sizeof(video_variable));
    memset(commerce_variable, 0x00, sizeof(commerce_variable));
    memset(carousel_variable, 0x00, sizeof(carousel_variable));
    memset(reserve, 0x00, sizeof(reserve));

    snprintf(telco_name, sizeof(telco_name), q_name);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL EXECUTE
    BEGIN

    //proc_get_ftk_msg_v3(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :button, :img_path, :img_link, :res_method, :timeout, :ad_flag, :wide, :kko_img_url, :ot_sqlcode, :ot_sqlmsg);
    proc_get_ftkup_msg_v2(:telco_name, :cmms_id, :var_type, :sender_key, :dst_addr,                          
                          :res_method, :encoding_type, :chat_bubble_type, 
                          :targeting, :tmpl_cd, :app_user_id, :push_alarm,
                          :reseller_code, :unsub_phone, :unsub_auth, :add_content,
                          :adult, :headers, :message_variable, 
                          :button_variable, :coupon_variable, :image_variable, :video_variable, 
                          :commerce_variable, :carousel_variable, :attachment, :reserve, 
                          :ot_sqlcode, :ot_sqlmsg);
    END;
    END-EXEC;

    //mms_id = atoll(cmms_id);
    //sprintf(s_mms_id, "%s", cmms_id);

    switch(ot_sqlcode) {
        case 0: {
	        mms_id = atoll(cmms_id);
        	snprintf( s_mms_id, sizeof(s_mms_id), "%s", cmms_id);

        	mapSend["mms_id"] = trimR(s_mms_id).c_str();
        	mapSend["sender_key"] = trimR(sender_key).c_str();
        	mapSend["dst_addr"] = trimR(dst_addr).c_str();
        	mapSend["res_method"] = trimR(res_method).c_str();
        	mapSend["timeout"] = trimR(timeout).c_str();
        	mapSend["encoding_type"] = trimR(encoding_type).c_str();
        	mapSend["chat_bubble_type"] = trimR(chat_bubble_type).c_str();
        	mapSend["targeting"] = trimR(targeting).c_str();
        	mapSend["tmpl_cd"] = trimR(tmpl_cd).c_str();

        	char temp_app_user_id[20+1] = {};        	
        	snprintf(temp_app_user_id, sizeof(temp_app_user_id), "%s", app_user_id);
        	removeAllSpaces(temp_app_user_id);
        	//mapSend["app_user_id"] = trimR(app_user_id).c_str();
        	mapSend["app_user_id"] = temp_app_user_id;
#if (DEBUG >= 5)
        	sprintf(tmpLog3, "CDatabaseORA::getMsgData_V4() app_user_id str[%s] hex[%02x %02x %02x %02x] len[%d]",
					app_user_id,
					(unsigned char) app_user_id[0], (unsigned char) app_user_id[1],
					(unsigned char) app_user_id[2], (unsigned char) app_user_id[3],
					(int) strlen(app_user_id));
        	log3(tmpLog3, 0, 0);
        	sprintf(tmpLog3, "CDatabaseORA::getMsgData_V4() temp_app_user_id str[%s]",	temp_app_user_id);
        	log3(tmpLog3, 0, 0);
#endif
    		
        	mapSend["push_alarm"] = trimR(push_alarm).c_str();
        	mapSend["message_variable"] = trimR(message_variable).c_str();
        	mapSend["button_variable"] = trimR(button_variable).c_str();
        	mapSend["coupon_variable"] = trimR(coupon_variable).c_str();
        	mapSend["image_variable"] = trimR(image_variable).c_str();
        	mapSend["video_variable"] = trimR(video_variable).c_str();
        	mapSend["commerce_variable"] = trimR(commerce_variable).c_str();
        	mapSend["carousel_variable"] = trimR(carousel_variable).c_str();
        	mapSend["reserve"] = trimR(reserve).c_str();

        	return mms_id;
        }
        case -5:
        case -1405:
            break;
        default:
            
            sprintf(tmpLog3, "CDatabaseORA::getMsgData_V4() ERROR [%d][%s][%s]", ot_sqlcode, ot_sqlmsg, sqlca.sqlerrm.sqlerrmc);
            log3(tmpLog3, 0, 0);
            return -1;
    }

    return 0;
}

int CDatabaseORA::getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ctn_id;
	char ctn_name[50+1];
	char ctn_type[50+1];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND DO BREAK;
    EXEC SQL WHENEVER SQLERROR goto sql_error;   /* don't trap errors */

	int ctn_cnt=0;

	memset(ctn_name, 0x00, sizeof ctn_name);
	memset(ctn_type, 0x00, sizeof ctn_type);

	ctn_id = cid;

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL DECLARE C1 CURSOR FOR
		SELECT CTN_NAME, CTN_MIME FROM TBL_MMS_CTN WHERE CTN_ID = :ctn_id ORDER BY CTN_SEQ;
    EXEC SQL OPEN C1;

	while(1)
	{
		EXEC SQL FETCH C1 INTO :ctn_name, :ctn_type;

		if(sqlca.sqlcode == 1403)
		{
			sprintf(tmpLog3, "CDatabaseORA::getCtnData() CNT[%d] ctn_id : [%d] NOT FOUND", ctn_cnt, ctn_id);
			log3(tmpLog3, 0, 0);
			break;
		}
		else if(sqlca.sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::getCtnData() CNT[%d] ctn_id : [%d] SQL ERROR", ctn_cnt, ctn_id);
			log3(tmpLog3, 0, 0);
		}

		vtCtnData.push_back(trimR(ctn_name).c_str());
		vtCtnData.push_back(trimR(ctn_type).c_str());
		ctn_cnt++;

		//sprintf(tmpLog3, "ctn_id[%d] : [%d] ctn_name : [%s] ctn_type : [%s]", ctn_cnt, ctn_id, ctn_name, ctn_type);
		//log3(tmpLog3, 0, 0);
	}
    EXEC SQL CLOSE C1;

	return ctn_cnt;

sql_error:
	sprintf(tmpLog3, "\n CDatabaseORA::getCtnData() ERROR[%d][%70s] \n", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
	log3(tmpLog3, 0, 0);
	return -1;
}

int CDatabaseORA::setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	long long mms_id;
	char cmms_id[20+1];
	char msg_id[50+1];
	char ack_code[10+1];
	char ack_text[200+1];
	int telco_id;
	char ot_sqlmsg[256];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof(ot_sqlmsg));
	memset(msg_id, 0x00, sizeof(msg_id));
	memset(ack_code, 0x00, sizeof(ack_code));
	memset(ack_text, 0x00, sizeof(ack_text));
	memset(cmms_id, 0x00, sizeof(cmms_id));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();

//	mms_id = atoi(string(*(itrData + 2)).c_str());	//mms_id
	mms_id = atoll(string(*(itrData + 2)).c_str());
	//sprintf(cmms_id, "%lld",mms_id);
	snprintf(cmms_id, sizeof(cmms_id), "%lld",mms_id);
	//strcpy(msg_id, string(*(itrData + 2)).c_str());	//msg_id
	strncpy(msg_id, string(*(itrData + 2)).c_str(), sizeof(msg_id)-1);	//msg_id
	//strcpy(ack_code, string(*itrData).c_str());		// result code
	strncpy(ack_code, string(*itrData).c_str(), sizeof(ack_code)-1);		// result code
	//strcpy(ack_text, string(*(itrData + 1)).c_str());	// result_message
	strncpy(ack_text, string(*(itrData + 1)).c_str(), sizeof(ack_text)-1);	// result_message
	telco_id = telcoid;	// QUEUE_SKB_BCK queue number

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_ack(:cmms_id, :msg_id, :ack_code, :ack_text, :telco_id, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	if (ot_sqlcode!=0) {
		sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR MMSID[%lld][%d][%s]", mms_id, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

int CDatabaseORA::setReportData(int telcoid, sql_context ctx, map<string, string> &mapReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	int ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[32+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id,	0x00, sizeof(cmms_id));

	char sNum[24+1];
	char rNum[24+1];
	memset(sNum, 0x00, 24+1);
	memset(rNum, 0x00, 24+1);

	map<string, string>::iterator FindItr;
	
	FindItr = mapReport.find("msg_id");
	
	mms_id = atoll(FindItr->second.c_str());
	
	//sprintf(cmms_id,"%s", FindItr->second.c_str());
	snprintf(cmms_id, sizeof(cmms_id), "%s", FindItr->second.c_str());
	
	//sprintf(msg_id, "%s", FindItr->second.c_str());	//msg_id
	snprintf(msg_id, sizeof(msg_id),  "%s", FindItr->second.c_str());	//msg_id
	//sprintf(rNum, "%s", (char*)string(*(itrData + 2)).c_str()); //rcv_phn_id
	
	FindItr = mapReport.find("dlv_date");
	//sprintf(dlv_date, "%s", FindItr->second.c_str()); //reg_snd_dttm
	snprintf(dlv_date, sizeof(dlv_date),  "%s", FindItr->second.c_str()); //reg_snd_dttm
	if (strcmp(dlv_date, "") == 0 || strcmp(dlv_date, "              ") == 0)
	{
		//if there is no time sent to carrier, enter arbitrary value
		strcpy(dlv_date,"19710101000000");
	}

	FindItr = mapReport.find("res_code");
	//sprintf(res_code, "%s", FindItr->second.c_str()); //rslt_val
	snprintf(res_code, sizeof(res_code),  "%s", FindItr->second.c_str()); //rslt_val

	FindItr = mapReport.find("end_telco");
	//sprintf(end_telco, "%s", FindItr->second.c_str());
	snprintf(end_telco, sizeof(end_telco), "%s", FindItr->second.c_str());

	FindItr = mapReport.find("res_text");
	//sprintf(res_text, FindItr->second.c_str());
	strncpy(res_text, FindItr->second.c_str(), sizeof(res_text)-1);
	
	telco_id = telcoid;
	res_type = 0;

	//strcpy(snd_numb, sNum);
	FindItr = mapReport.find("rcv_numb");
	//sprintf(rcv_numb, FindItr->second.c_str());
	snprintf(rcv_numb, sizeof(rcv_numb), FindItr->second.c_str());
	
	int retry_cnt = 0;

	/*				
	cerr<<"cmms_id:"<<cmms_id<<endl;
	cerr<<"msg_id:"<<msg_id<<endl;
	cerr<<"dlv_date:"<<dlv_date<<endl;
	cerr<<"snd_numb:"<<snd_numb<<endl;
	cerr<<"rcv_numb:"<<rcv_numb<<endl;
	cerr<<"res_text:"<<res_text<<endl;
	cerr<<"telco_id:"<<telco_id<<endl;
	cerr<<"res_type:"<<res_type<<endl;
	cerr<<"end_telco:"<<end_telco<<endl;
	*/	
	
//retry:

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
	END;
	END-EXEC;

	if( ot_sqlcode != 0 )
	{
		/*if (strstr(ot_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			//memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry MMSID[%lld][%d][%.100s]", mms_id, ot_sqlcode, ot_sqlmsg );
			log3(tmpLog3, 0, 0);
			
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			
			goto retry;
		}*/
		
		if (strstr(ot_sqlmsg,"ORA-00001"))
		{
			ot_sqlcode = -1;
			//memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb already exist MMSID[%lld][%d][%.100s]", mms_id, ot_sqlcode, ot_sqlmsg );
			log3(tmpLog3, 0, 0);
			
			memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
			
			return 0;
		}

		sprintf(tmpLog3, "CDatabaseORA::setReportData() ERROR MMSID[%lld][%d][%.100s]", mms_id, ot_sqlcode, ot_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	if( ot_queue_sqlcode != 0 )
	{
		/*if (strstr(ot_queue_sqlmsg,"ORA-00001") && retry_cnt < 2)
		{
			ot_sqlcode = -1;
			memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
			retry_cnt++;
			sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_mms_rpt_queue retry MMSID[%lld][%d][%.100s]", mms_id, ot_queue_sqlcode, ot_queue_sqlmsg );
			log3(tmpLog3, 0, 0);
			goto retry;
		}*/

		sprintf(tmpLog3, "CDatabaseORA::setReportData() queue ERROR MMSID[%lld][%d][%.100s]", mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	/*if (retry_cnt > 0)
	{
		sprintf(tmpLog3, "CDatabaseORA::setReportData() proc_set_rpt_skb retry success");
		log3(tmpLog3, 0, 0);
	}*/
	
	return 0;
}

int CDatabaseORA::_putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
	char telco_name[24+1];
	long long mms_id;
	char cmms_id[30+1];
	char txt_path[256+1];
    struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

    memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
    memset(telco_name, 0x00, sizeof telco_name);
    memset(txt_path, 0x00, sizeof txt_path);
    memset(cmms_id, 0x00, sizeof(cmms_id));

	vector<string>::iterator itrData;
	//data input
	strcpy(telco_name, q_name);

	char szYYYYMM[32];
	memset(szYYYYMM, 0x00, 32);
	get_timestring("%04d%02d",time(NULL),szYYYYMM);
	trim(szYYYYMM,strlen(szYYYYMM));

	mms_id = mmsid;
	sprintf(cmms_id,"%lld",mmsid);
	sprintf(txt_path, "TXT_SSN/%s/%d.txt", szYYYYMM, mms_id);	// TXT_PATH
	char * pch;
	if (telcoid == 1)
	{
		pch = strstr (txt_path,"KTF");
		if (pch) strncpy (pch,"SKT",3);
		pch = strstr (txt_path,"LGT");
		if (pch) strncpy (pch,"SKT",3);
		pch = strstr (txt_path,"SSN");
		if (pch) strncpy (pch,"SKT",3);
	}

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_set_msg_retry_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

    if( ot_sqlcode != 0 )
    {
		sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() ERROR[%d][%.100s]", ot_sqlcode, ot_sqlmsg );
		log3(tmpLog3, 0, 0);
        return -1;
    }
    return 0;
}

}

/*
 * output time string.
 */
void get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
}

char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

void log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}

char* removeAllSpaces(char *str)
{
	int i, j;
	if (!str)
		return NULL;
	
	// front space remove
	for (j = 0; str[j] == ' ' || str[j] == 9 || str[j] == '\r' || str[j] == '\n'; j++)
		;
	
	// move string to front
	for (i = 0; str[j] != '\0'; i++, j++)
		str[i] = str[j];
	
	// remove space from back
	for (i--; (i >= 0) && (str[i] == ' ' || str[i] == 9 || str[i] == '\r' || str[i] == '\n'); i--)
		;
	str[i + 1] = '\0';
	return str;
}